import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../models/account.dart';
import 'add_account_screen.dart';
import 'account_details_screen.dart';
import 'add_amount_screen.dart';
import 'search_screen.dart';
import 'reports_screen.dart';
import 'all_accounts_screen.dart';
import '../widgets/app_drawer.dart';
import '../widgets/theme_toggle_button.dart';
import '../theme/modern_app_theme.dart';
import '../widgets/modern_ui_components.dart';
import '../services/responsive_service.dart';
import '../services/app_state_service.dart';
import '../services/custom_currency_service.dart';
import '../widgets/responsive_layout.dart';

/// الشاشة الرئيسية المبتكرة بتصميم غير تقليدي
class InnovativeHomeScreen extends StatefulWidget {
  const InnovativeHomeScreen({super.key});

  @override
  State<InnovativeHomeScreen> createState() => _InnovativeHomeScreenState();
}

class _InnovativeHomeScreenState extends State<InnovativeHomeScreen>
    with TickerProviderStateMixin {
  final CustomCurrencyService _currencyService = CustomCurrencyService();
  String _currencySymbol = 'SAR';

  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _slideController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الانيميشن
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateService>().loadAllData();
      _loadCurrencySymbol();
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await _currencyService.getDefaultCurrencySymbol();
    if (mounted) {
      setState(() {
        _currencySymbol = symbol;
      });
    }
  }

  Future<void> _addAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );
    if (result == true && mounted) {
      ModernUIComponents.showModernSnackBar(
        context: context,
        message: 'تم إضافة الحساب بنجاح',
        isSuccess: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateService>(
      builder: (context, appState, child) {
        final accounts = appState.accounts;
        final isLoading = appState.isLoading;
        final totalCredit = appState.totalPositiveBalance;
        final totalDebit = appState.totalNegativeBalance;

        return ResponsiveBuilder(
          builder: (context, deviceType) {
            return Scaffold(
              backgroundColor: const Color(0xFFFAFAFA),
              drawer:
                  ResponsiveService.shouldUseDrawer(context)
                      ? const AppDrawer()
                      : null,
              extendBodyBehindAppBar: true,
              appBar: _buildInnovativeAppBar(context, appState),
              body: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFFFFFFF),
                      Color(0xFFF8F9FA),
                      Color(0xFFFAFAFA),
                    ],
                  ),
                ),
                child: SafeArea(
                  child:
                      isLoading
                          ? _buildInnovativeLoader()
                          : accounts.isEmpty
                          ? _buildInnovativeWelcomeScreen()
                          : _buildInnovativeMainContent(
                            accounts,
                            totalCredit,
                            totalDebit,
                            appState,
                          ),
                ),
              ),
              floatingActionButton: _buildInnovativeFAB(),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerFloat,
            );
          },
        );
      },
    );
  }

  /// بناء شريط التطبيق المبتكر
  PreferredSizeWidget _buildInnovativeAppBar(
    BuildContext context,
    AppStateService appState,
  ) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              ModernAppTheme.primaryColor.withValues(alpha: 0.9),
              const Color(0xFF6366F1).withValues(alpha: 0.8),
              const Color(0xFF8B5CF6).withValues(alpha: 0.7),
            ],
          ),
        ),
      ),
      leading:
          ResponsiveService.shouldUseDrawer(context)
              ? Builder(
                builder:
                    (context) => IconButton(
                      icon: const Icon(Icons.menu_rounded, color: Colors.white),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
              )
              : null,
      title: Row(
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          const Text(
            'دفتر الحسابات',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 20,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
      actions: [
        const ThemeToggleButton(),
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: () => appState.refresh(),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  /// بناء مؤشر التحميل المبتكر
  Widget _buildInnovativeLoader() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        ModernAppTheme.primaryColor,
                        const Color(0xFF6366F1),
                        const Color(0xFF8B5CF6),
                      ],
                    ),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet_rounded,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          const Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              color: Color(0xFF4A5568),
              fontSize: 16,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شاشة الترحيب المبتكرة
  Widget _buildInnovativeWelcomeScreen() {
    return SlideTransition(
      position: _slideAnimation,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          ModernAppTheme.primaryColor,
                          const Color(0xFF6366F1),
                          const Color(0xFF8B5CF6),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: ModernAppTheme.primaryColor.withValues(
                            alpha: 0.5,
                          ),
                          blurRadius: 30,
                          spreadRadius: 10,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet_rounded,
                      color: Colors.white,
                      size: 60,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 40),
            const Text(
              'مرحباً بك في دفتر الحسابات!',
              style: TextStyle(
                color: Color(0xFF1A1A1A),
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'ابدأ رحلتك المالية بإضافة أول حساب\nوتتبع جميع معاملاتك بسهولة',
              style: TextStyle(
                color: Color(0xFF4A5568),
                fontSize: 16,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            _buildInnovativeButton(
              text: 'إضافة حساب جديد',
              onPressed: _addAccount,
              icon: Icons.add_rounded,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي المبتكر
  Widget _buildInnovativeMainContent(
    List<Account> accounts,
    double totalCredit,
    double totalDebit,
    AppStateService appState,
  ) {
    return SlideTransition(
      position: _slideAnimation,
      child: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // الملخص المالي الدائري المبتكر
          SliverToBoxAdapter(
            child: _buildCircularFinancialSummary(totalCredit, totalDebit),
          ),
          // شريط الإجراءات السريعة المبتكر
          SliverToBoxAdapter(child: _buildInnovativeActionBar()),
          // عنوان الحسابات
          SliverToBoxAdapter(
            child: _buildInnovativeAccountsHeader(accounts.length),
          ),
          // شبكة الحسابات المبتكرة
          _buildInnovativeAccountsGrid(accounts, appState),
        ],
      ),
    );
  }

  /// بناء الملخص المالي الدائري المبتكر
  Widget _buildCircularFinancialSummary(double totalCredit, double totalDebit) {
    final netBalance = totalCredit - totalDebit;

    return Container(
      margin: const EdgeInsets.all(20),
      height: 280,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الخلفية الدائرية المتحركة
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * 0.1,
                child: Container(
                  width: 250,
                  height: 250,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        ModernAppTheme.primaryColor.withValues(alpha: 0.1),
                        const Color(0xFF6366F1).withValues(alpha: 0.15),
                        const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                        ModernAppTheme.primaryColor.withValues(alpha: 0.1),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          // الدائرة الرئيسية
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  ModernAppTheme.primaryColor,
                  const Color(0xFF6366F1),
                  const Color(0xFF8B5CF6),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: ModernAppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 0,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 40,
                  spreadRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.account_balance_wallet_rounded,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  'الرصيد الصافي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontFamily: 'Cairo',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${netBalance.toStringAsFixed(2)} $_currencySymbol',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          // مؤشرات الدائن والمدين
          Positioned(
            top: 20,
            right: 20,
            child: _buildFloatingIndicator(
              'لنا',
              totalCredit,
              ModernAppTheme.successColor,
              Icons.trending_up_rounded,
            ),
          ),
          Positioned(
            bottom: 20,
            left: 20,
            child: _buildFloatingIndicator(
              'علينا',
              totalDebit,
              ModernAppTheme.errorColor,
              Icons.trending_down_rounded,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر عائم
  Widget _buildFloatingIndicator(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (_pulseAnimation.value - 0.8) * 0.3,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.4),
                  blurRadius: 15,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: Colors.white, size: 16),
                const SizedBox(width: 6),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    Text(
                      '${amount.toStringAsFixed(0)} $_currencySymbol',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء شريط الإجراءات السريعة المبتكر
  Widget _buildInnovativeActionBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 80,
      child: Row(
        children: [
          Expanded(
            child: _buildHexagonButton(
              'البحث',
              Icons.search_rounded,
              const Color(0xFF6366F1),
              () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SearchScreen()),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildHexagonButton(
              'التقارير',
              Icons.analytics_rounded,
              const Color(0xFF8B5CF6),
              () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ReportsScreen()),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildHexagonButton(
              'الحسابات',
              Icons.list_rounded,
              const Color(0xFFEC4899),
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AllAccountsScreen(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر سداسي مبتكر
  Widget _buildHexagonButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return GestureDetector(
      onTap: onPressed,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.9 + (_pulseAnimation.value - 0.8) * 0.1,
            child: Container(
              height: 70,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [color, color.withValues(alpha: 0.8)],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 20,
                    spreadRadius: 2,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: Colors.white, size: 24),
                  const SizedBox(height: 4),
                  Text(
                    text,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء عنوان الحسابات المبتكر
  Widget _buildInnovativeAccountsHeader(int accountsCount) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [ModernAppTheme.primaryColor, const Color(0xFF6366F1)],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: ModernAppTheme.primaryColor.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.account_balance_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الحسابات',
                  style: TextStyle(
                    color: Color(0xFF1A1A1A),
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                Text(
                  '$accountsCount حساب نشط',
                  style: const TextStyle(
                    color: Color(0xFF4A5568),
                    fontSize: 14,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة الحسابات المبتكرة
  Widget _buildInnovativeAccountsGrid(
    List<Account> accounts,
    AppStateService appState,
  ) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.75,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final account = accounts[index];
          return _buildInnovativeAccountCard(account, appState, index);
        }, childCount: accounts.length),
      ),
    );
  }

  /// بناء كارت حساب مبتكر
  Widget _buildInnovativeAccountCard(
    Account account,
    AppStateService appState,
    int index,
  ) {
    final isPositive = account.isPositive;
    final colors = _getCardColors(index);

    return GestureDetector(
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AccountDetailsScreen(account: account),
          ),
        );
        if (result == true) {
          appState.refresh();
        }
      },
      onLongPress: () => _showAccountActions(account, appState),
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.95 + (_pulseAnimation.value - 0.8) * 0.05,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: colors,
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: colors.first.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 6),
                  ),
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 25,
                    spreadRadius: 3,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(25),
                child: Stack(
                  children: [
                    // خلفية متحركة
                    Positioned.fill(
                      child: AnimatedBuilder(
                        animation: _rotationAnimation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _rotationAnimation.value * 0.05,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: SweepGradient(
                                  colors: [
                                    Colors.white.withValues(alpha: 0.1),
                                    Colors.transparent,
                                    Colors.white.withValues(alpha: 0.05),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    // المحتوى
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // أيقونة الحالة
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              isPositive
                                  ? Icons.trending_up_rounded
                                  : Icons.trending_down_rounded,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                          const SizedBox(height: 12),
                          // اسم الحساب
                          Text(
                            account.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Cairo',
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 6),
                          // نوع الحساب
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              isPositive ? 'دائن' : 'مدين',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 9,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ),
                          const Spacer(),
                          // الرصيد
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Column(
                              children: [
                                const Text(
                                  'الرصيد',
                                  style: TextStyle(
                                    color: Colors.white70,
                                    fontSize: 9,
                                    fontFamily: 'Cairo',
                                  ),
                                ),
                                const SizedBox(height: 2),
                                FittedBox(
                                  child: Text(
                                    '${account.balance.toStringAsFixed(2)} $_currencySymbol',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// الحصول على ألوان الكارت حسب الفهرس
  List<Color> _getCardColors(int index) {
    final colorSets = [
      [ModernAppTheme.primaryColor, const Color(0xFF6366F1)],
      [const Color(0xFF8B5CF6), const Color(0xFFEC4899)],
      [const Color(0xFF06B6D4), const Color(0xFF3B82F6)],
      [const Color(0xFF10B981), const Color(0xFF059669)],
      [const Color(0xFFF59E0B), const Color(0xFFEF4444)],
      [const Color(0xFF8B5CF6), const Color(0xFF6366F1)],
    ];
    return colorSets[index % colorSets.length];
  }

  /// عرض إجراءات الحساب
  void _showAccountActions(Account account, AppStateService appState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
              ),
              borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
              boxShadow: [
                BoxShadow(
                  color: Color(0x1A000000),
                  blurRadius: 20,
                  offset: Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFF4A5568).withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        account.name,
                        style: const TextStyle(
                          color: Color(0xFF1A1A1A),
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 20),
                      _buildActionButton(
                        'إضافة معاملة',
                        Icons.add_rounded,
                        const Color(0xFF10B981),
                        () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) =>
                                      AddAmountScreen(account: account),
                            ),
                          ).then((result) {
                            if (result == true) {
                              appState.refresh();
                            }
                          });
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildActionButton(
                        'تعديل الحساب',
                        Icons.edit_rounded,
                        const Color(0xFF3B82F6),
                        () {
                          Navigator.pop(context);
                          ModernUIComponents.showModernSnackBar(
                            context: context,
                            message: 'ميزة تعديل الحساب قيد التطوير',
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildActionButton(
                        'حذف الحساب',
                        Icons.delete_rounded,
                        const Color(0xFFEF4444),
                        () {
                          Navigator.pop(context);
                          _showDeleteConfirmation(account, appState);
                        },
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Account account, AppStateService appState) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFFFFFFFF),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: const Text(
              'تأكيد الحذف',
              style: TextStyle(color: Color(0xFF1A1A1A), fontFamily: 'Cairo'),
            ),
            content: Text(
              'هل أنت متأكد من حذف حساب "${account.name}"؟',
              style: const TextStyle(
                color: Color(0xFF4A5568),
                fontFamily: 'Cairo',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(
                    color: Color(0xFF4A5568),
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await appState.deleteAccount(account.id);
                  if (mounted) {
                    ModernUIComponents.showModernSnackBar(
                      context: context,
                      message: 'تم حذف الحساب بنجاح',
                      isSuccess: true,
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                  foregroundColor: Colors.white,
                ),
                child: const Text('حذف', style: TextStyle(fontFamily: 'Cairo')),
              ),
            ],
          ),
    );
  }

  /// بناء زر الإضافة العائم المبتكر
  Widget _buildInnovativeFAB() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  ModernAppTheme.primaryColor,
                  const Color(0xFF6366F1),
                  const Color(0xFF8B5CF6),
                ],
              ),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: ModernAppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 6),
                ),
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 25,
                  spreadRadius: 3,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: FloatingActionButton.extended(
              onPressed: _addAccount,
              backgroundColor: Colors.transparent,
              elevation: 0,
              icon: const Icon(
                Icons.add_rounded,
                color: Colors.white,
                size: 28,
              ),
              label: const Text(
                'إضافة حساب',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  fontFamily: 'Cairo',
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء زر مبتكر
  Widget _buildInnovativeButton({
    required String text,
    required VoidCallback onPressed,
    required IconData icon,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [ModernAppTheme.primaryColor, const Color(0xFF6366F1)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: ModernAppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 20,
            spreadRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
        ),
        icon: Icon(icon, size: 24),
        label: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }
}
