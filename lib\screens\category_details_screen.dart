import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/account.dart';
import '../services/category_service.dart';
import '../services/custom_currency_service.dart';
import 'currency_category_accounts_screen.dart';
import 'add_account_screen.dart';

/// صفحة تفاصيل التصنيف
class CategoryDetailsScreen extends StatefulWidget {
  final Category category;

  const CategoryDetailsScreen({super.key, required this.category});

  @override
  State<CategoryDetailsScreen> createState() => _CategoryDetailsScreenState();
}

class _CategoryDetailsScreenState extends State<CategoryDetailsScreen> {
  final CategoryService _categoryService = CategoryService();
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  List<Account> _accounts = [];
  Map<String, List<Account>> _accountsByCurrency = {};
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategoryData();
  }

  /// تحميل بيانات التصنيف
  Future<void> _loadCategoryData() async {
    setState(() => _isLoading = true);
    try {
      final accounts = await _categoryService.getAccountsByCategory(
        widget.category.id,
      );
      final statistics = await _categoryService.getCategoryStatistics(
        widget.category.id,
      );

      // تجميع الحسابات حسب العملة
      final accountsByCurrency = <String, List<Account>>{};
      for (final account in accounts) {
        final currency = account.currencyId ?? 'SAR';
        accountsByCurrency.putIfAbsent(currency, () => []).add(account);
      }

      setState(() {
        _accounts = accounts;
        _accountsByCurrency = accountsByCurrency;
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorMessage('خطأ في تحميل بيانات التصنيف: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFAB(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              widget.category.color,
              widget.category.color.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: widget.category.color.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(widget.category.icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              widget.category.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      ),
      centerTitle: true,
      leading: Container(
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFD32F2F), Color(0xFFE53935)],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFD32F2F).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(
            Icons.arrow_back_rounded,
            color: Colors.white,
            size: 22,
          ),
          onPressed: () => Navigator.pop(context, true),
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Color(0xFF4A5FBF)),
            SizedBox(height: 16),
            Text(
              'جاري تحميل بيانات التصنيف...',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Cairo',
                color: Color(0xFF6B7280),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة معلومات التصنيف
            _buildCategoryInfoCard(),
            const SizedBox(height: 20),
            // إحصائيات التصنيف
            _buildStatisticsCard(),
            const SizedBox(height: 20),
            // الحسابات حسب العملة
            if (_accountsByCurrency.isNotEmpty) ...[
              _buildSectionTitle('الحسابات حسب العملة'),
              const SizedBox(height: 12),
              ..._accountsByCurrency.entries.map(
                (entry) => _buildCurrencyCard(entry.key, entry.value),
              ),
            ] else
              _buildEmptyState(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات التصنيف
  Widget _buildCategoryInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: widget.category.borderColor),
        boxShadow: [
          BoxShadow(
            color: widget.category.color.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: widget.category.colorWithOpacity,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  widget.category.icon,
                  color: widget.category.color,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.category.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.category.description,
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: 'Cairo',
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الإحصائيات
  Widget _buildStatisticsCard() {
    final totalAccounts = _statistics['totalAccounts'] ?? 0;
    final currencyBalances =
        _statistics['currencyBalances'] as Map<String, dynamic>? ?? {};

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4A5FBF).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.analytics_rounded,
                  color: Color(0xFF4A5FBF),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'إحصائيات التصنيف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي الحسابات',
                  totalAccounts.toString(),
                  Icons.account_circle_rounded,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'العملات المستخدمة',
                  currencyBalances.length.toString(),
                  Icons.currency_exchange_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      child: Column(
        children: [
          Icon(icon, color: const Color(0xFF4A5FBF), size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'Cairo',
              color: Color(0xFF6B7280),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
        color: Color(0xFF1F2937),
      ),
    );
  }

  /// بناء بطاقة العملة
  Widget _buildCurrencyCard(String currencyId, List<Account> accounts) {
    final totalBalance = accounts.fold<double>(
      0,
      (sum, account) => sum + account.balance,
    );
    final isPositive = totalBalance >= 0;
    final color =
        isPositive ? const Color(0xFF10B981) : const Color(0xFFEF4444);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToCurrencyAccounts(currencyId),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.currency_exchange_rounded,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currencyId,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${accounts.length} حساب',
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'Cairo',
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${totalBalance.toStringAsFixed(2)} $currencyId',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Color(0xFF9CA3AF),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: widget.category.colorWithOpacity,
              shape: BoxShape.circle,
            ),
            child: Icon(
              widget.category.icon,
              size: 64,
              color: widget.category.color,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'لا توجد حسابات في هذا التصنيف',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
              color: Color(0xFF374151),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ابدأ بإضافة حساب جديد لهذا التصنيف',
            style: TextStyle(
              fontSize: 14,
              fontFamily: 'Cairo',
              color: Color(0xFF6B7280),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFAB() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            widget.category.color,
            widget.category.color.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: widget.category.color.withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _addNewAccount,
        backgroundColor: Colors.transparent,
        elevation: 0,
        icon: const Icon(Icons.add_rounded, color: Colors.white),
        label: const Text(
          'إضافة حساب',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }

  /// التنقل لحسابات العملة
  void _navigateToCurrencyAccounts(String currencyId) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => CurrencyCategoryAccountsScreen(
              category: widget.category,
              currencyId: currencyId,
            ),
      ),
    );

    if (result == true) {
      _loadCategoryData();
    }
  }

  /// إضافة حساب جديد
  void _addNewAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AddAccountScreen(
              preselectedCategoryId: widget.category.id,
              preselectedCategoryName: widget.category.name,
            ),
      ),
    );

    if (result != null) {
      _loadCategoryData();
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: const Color(0xFFEF4444),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
