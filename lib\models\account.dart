class Account {
  final String id;
  final String name;
  final String? phoneNumber; // رقم الهاتف
  final String? address; // العنوان
  final String? notes; // ملاحظات إضافية
  final double balance;
  final double initialBalance; // الرصيد الابتدائي
  final bool initialIsPositive; // نوع الرصيد الابتدائي
  final int transactionCount;
  final bool isPositive;
  final DateTime? createdAt;
  final String? currencyId; // معرف العملة

  Account({
    required this.id,
    required this.name,
    this.phoneNumber,
    this.address,
    this.notes,
    required this.balance,
    required this.initialBalance,
    required this.initialIsPositive,
    required this.transactionCount,
    required this.isPositive,
    this.createdAt,
    this.currencyId,
  });

  Account copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? address,
    String? notes,
    double? balance,
    double? initialBalance,
    bool? initialIsPositive,
    int? transactionCount,
    bool? isPositive,
    DateTime? createdAt,
    String? currencyId,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      balance: balance ?? this.balance,
      initialBalance: initialBalance ?? this.initialBalance,
      initialIsPositive: initialIsPositive ?? this.initialIsPositive,
      transactionCount: transactionCount ?? this.transactionCount,
      isPositive: isPositive ?? this.isPositive,
      createdAt: createdAt ?? this.createdAt,
      currencyId: currencyId ?? this.currencyId,
    );
  }

  // Convert to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'address': address,
      'notes': notes,
      'balance': balance,
      'initialBalance': initialBalance,
      'initialIsPositive': initialIsPositive ? 1 : 0,
      'transactionCount': transactionCount,
      'isPositive': isPositive ? 1 : 0,
      'createdAt':
          createdAt?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch,
      'currencyId': currencyId,
    };
  }

  // Create from Map (database)
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'],
      name: map['name'],
      phoneNumber: map['phoneNumber'],
      address: map['address'],
      notes: map['notes'],
      balance: map['balance'],
      initialBalance:
          map['initialBalance'] ??
          map['balance'], // للتوافق مع البيانات القديمة
      initialIsPositive: (map['initialIsPositive'] ?? map['isPositive']) == 1,
      transactionCount: map['transactionCount'],
      isPositive: map['isPositive'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      currencyId: map['currencyId'],
    );
  }

  // Format balance with thousands separator and currency
  String get formattedBalance {
    final formattedNumber = balance
        .toStringAsFixed(2)
        .replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );

    // Use default currency symbol - this will be updated by the UI layer
    // to use the actual default currency from the database
    return '$formattedNumber SAR';
  }

  // Format balance with specific currency symbol
  String formattedBalanceWithCurrency(String currencySymbol) {
    final formattedNumber = balance
        .toStringAsFixed(2)
        .replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
    return '$formattedNumber $currencySymbol';
  }

  // Get account type in Arabic
  String get typeInArabic {
    return isPositive ? 'دائن' : 'مدين';
  }

  // Get formatted creation date
  String get formattedCreatedDate {
    if (createdAt == null) return '';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
}
