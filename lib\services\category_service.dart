import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/category.dart';
import '../models/account.dart';

/// خدمة إدارة التصنيفات
class CategoryService {
  static final CategoryService _instance = CategoryService._internal();
  factory CategoryService() => _instance;
  CategoryService._internal();

  static Database? _database;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'accounting_app.db');
    return await openDatabase(
      path,
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// إنشاء الجداول
  Future<void> _onCreate(Database db, int version) async {
    await createCategoriesTable(db);
    await _createAccountsTable(db);
  }

  /// ترقية قاعدة البيانات
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await createCategoriesTable(db);
      await updateAccountsTableForCategories(db);
    }
  }

  /// إنشاء جدول الحسابات
  Future<void> _createAccountsTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS accounts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phoneNumber TEXT,
        address TEXT,
        notes TEXT,
        balance REAL NOT NULL,
        initialBalance REAL NOT NULL,
        initialIsPositive INTEGER NOT NULL,
        transactionCount INTEGER NOT NULL,
        isPositive INTEGER NOT NULL,
        createdAt INTEGER NOT NULL,
        currencyId TEXT,
        categoryId TEXT,
        categoryName TEXT
      )
    ''');
  }

  /// إنشاء جدول التصنيفات
  Future<void> createCategoriesTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        iconCodePoint INTEGER NOT NULL,
        iconFontFamily TEXT,
        colorValue INTEGER NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER
      )
    ''');
  }

  /// تحديث جدول الحسابات لإضافة التصنيفات
  Future<void> updateAccountsTableForCategories(Database db) async {
    try {
      // التحقق من وجود العمود categoryId
      final result = await db.rawQuery("PRAGMA table_info(accounts)");
      final columns = result.map((row) => row['name'] as String).toList();

      if (!columns.contains('categoryId')) {
        await db.execute('ALTER TABLE accounts ADD COLUMN categoryId TEXT');
      }

      if (!columns.contains('categoryName')) {
        await db.execute('ALTER TABLE accounts ADD COLUMN categoryName TEXT');
      }
    } catch (e) {
      // تجاهل الأخطاء إذا كانت الأعمدة موجودة بالفعل
    }
  }

  /// إضافة التصنيفات الافتراضية
  Future<void> insertDefaultCategories() async {
    final db = await database;
    final defaultCategories = Category.getDefaultCategories();

    for (final category in defaultCategories) {
      await db.insert(
        'categories',
        category.toMap(),
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );
    }
  }

  /// الحصول على جميع التصنيفات
  Future<List<Category>> getAllCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      orderBy: 'createdAt ASC',
    );

    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// الحصول على تصنيف بالمعرف
  Future<Category?> getCategoryById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Category.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة تصنيف جديد
  Future<void> insertCategory(Category category) async {
    final db = await database;
    await db.insert(
      'categories',
      category.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث تصنيف
  Future<void> updateCategory(Category category) async {
    final db = await database;
    await db.update(
      'categories',
      category.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  /// حذف تصنيف
  Future<void> deleteCategory(String id) async {
    final db = await database;

    // تحديث الحسابات المرتبطة بالتصنيف
    await db.update(
      'accounts',
      {'categoryId': null, 'categoryName': null},
      where: 'categoryId = ?',
      whereArgs: [id],
    );

    // حذف التصنيف
    await db.delete('categories', where: 'id = ?', whereArgs: [id]);
  }

  /// الحصول على الحسابات حسب التصنيف
  Future<List<Account>> getAccountsByCategory(String categoryId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'categoryId = ?',
      whereArgs: [categoryId],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  /// الحصول على الحسابات حسب التصنيف والعملة
  Future<List<Account>> getAccountsByCategoryAndCurrency(
    String categoryId,
    String currencyId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'categoryId = ? AND currencyId = ?',
      whereArgs: [categoryId, currencyId],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات التصنيف
  Future<Map<String, dynamic>> getCategoryStatistics(String categoryId) async {
    final db = await database;

    // عدد الحسابات
    final accountCountResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM accounts WHERE categoryId = ?',
      [categoryId],
    );
    final accountCount = accountCountResult.first['count'] as int;

    // الأرصدة حسب العملة
    final balanceResult = await db.rawQuery(
      '''
      SELECT currencyId, SUM(balance) as totalBalance, COUNT(*) as accountCount
      FROM accounts 
      WHERE categoryId = ? 
      GROUP BY currencyId
    ''',
      [categoryId],
    );

    final currencyBalances = <String, Map<String, dynamic>>{};
    for (final row in balanceResult) {
      final currencyId = row['currencyId'] as String? ?? 'SAR';
      currencyBalances[currencyId] = {
        'totalBalance': row['totalBalance'] as double,
        'accountCount': row['accountCount'] as int,
      };
    }

    return {
      'totalAccounts': accountCount,
      'currencyBalances': currencyBalances,
    };
  }

  /// البحث في التصنيفات
  Future<List<Category>> searchCategories(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'name LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// الحصول على العملات المستخدمة في التصنيف
  Future<List<String>> getCurrenciesInCategory(String categoryId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT DISTINCT currencyId 
      FROM accounts 
      WHERE categoryId = ? AND currencyId IS NOT NULL
      ORDER BY currencyId ASC
    ''',
      [categoryId],
    );

    return maps.map((map) => map['currencyId'] as String).toList();
  }

  /// التحقق من إمكانية حذف التصنيف
  Future<bool> canDeleteCategory(String categoryId) async {
    final accounts = await getAccountsByCategory(categoryId);
    return accounts.isEmpty;
  }

  /// تحديث تصنيف الحسابات
  Future<void> updateAccountCategory(
    String accountId,
    String? categoryId,
    String? categoryName,
  ) async {
    final db = await database;
    await db.update(
      'accounts',
      {'categoryId': categoryId, 'categoryName': categoryName},
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }
}
