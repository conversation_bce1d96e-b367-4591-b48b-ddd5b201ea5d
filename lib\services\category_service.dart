import '../models/category.dart';
import '../models/account.dart';
import '../database/database_helper.dart';

class CategoryService {
  static final CategoryService _instance = CategoryService._internal();
  factory CategoryService() => _instance;
  CategoryService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<void> insertDefaultCategories() async {
    return;
  }

  Future<List<Category>> getAllCategories() async {
    return await _databaseHelper.getAllCategories();
  }

  Future<Category?> getCategoryById(String id) async {
    return await _databaseHelper.getCategoryById(id);
  }

  Future<void> insertCategory(Category category) async {
    await _databaseHelper.insertCategory(category);
  }

  Future<void> updateCategory(Category category) async {
    await _databaseHelper.updateCategory(category);
  }

  Future<void> deleteCategory(String id) async {
    await _databaseHelper.deleteCategory(id);
  }

  Future<List<Account>> getAccountsByCategory(String categoryId) async {
    return await _databaseHelper.getAccountsByCategory(categoryId);
  }

  Future<List<Account>> getAccountsByCategoryAndCurrency(
    String categoryId,
    String currencyId,
  ) async {
    final allAccounts = await _databaseHelper.getAccountsByCategory(categoryId);
    return allAccounts
        .where((account) => account.currencyId == currencyId)
        .toList();
  }

  Future<Map<String, dynamic>> getCategoryStatistics(String categoryId) async {
    final accounts = await _databaseHelper.getAccountsByCategory(categoryId);

    final accountCount = accounts.length;

    final currencyBalances = <String, Map<String, dynamic>>{};
    for (final account in accounts) {
      final currencyId = account.currencyId ?? 'SAR';
      if (!currencyBalances.containsKey(currencyId)) {
        currencyBalances[currencyId] = {'totalBalance': 0.0, 'accountCount': 0};
      }
      currencyBalances[currencyId]!['totalBalance'] =
          (currencyBalances[currencyId]!['totalBalance'] as double) +
          account.balance;
      currencyBalances[currencyId]!['accountCount'] =
          (currencyBalances[currencyId]!['accountCount'] as int) + 1;
    }

    return {
      'totalAccounts': accountCount,
      'currencyBalances': currencyBalances,
    };
  }

  Future<List<Category>> searchCategories(String query) async {
    final allCategories = await _databaseHelper.getAllCategories();
    return allCategories
        .where(
          (category) =>
              category.name.toLowerCase().contains(query.toLowerCase()) ||
              category.description.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();
  }

  Future<List<String>> getCurrenciesInCategory(String categoryId) async {
    final accounts = await _databaseHelper.getAccountsByCategory(categoryId);
    final currencies = <String>{};
    for (final account in accounts) {
      if (account.currencyId != null) {
        currencies.add(account.currencyId!);
      }
    }
    return currencies.toList()..sort();
  }

  Future<bool> canDeleteCategory(String categoryId) async {
    final accounts = await _databaseHelper.getAccountsByCategory(categoryId);
    return accounts.isEmpty;
  }

  Future<void> updateAccountCategory(
    String accountId,
    String? categoryId,
    String? categoryName,
  ) async {
    final account = await _databaseHelper.getAccount(accountId);
    if (account != null) {
      final updatedAccount = account.copyWith(
        categoryId: categoryId,
        categoryName: categoryName,
      );
      await _databaseHelper.updateAccount(updatedAccount);
    }
  }
}
