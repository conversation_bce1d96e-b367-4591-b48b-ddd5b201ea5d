import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/account.dart';
import '../services/app_state_service.dart';
import '../services/custom_currency_service.dart';

import 'account_details_screen.dart';
import 'add_amount_screen.dart';
import 'add_account_screen.dart';
import 'modern_settings_screen.dart';
import 'currency_management_screen.dart';
import 'comprehensive_reports_screen.dart';
import 'categories_management_screen.dart';

/// صفحة جميع الحسابات المبتكرة بتصميم جدول أنيق
class InnovativeAllAccountsScreen extends StatefulWidget {
  const InnovativeAllAccountsScreen({super.key});

  @override
  State<InnovativeAllAccountsScreen> createState() =>
      _InnovativeAllAccountsScreenState();
}

/// إعادة توجيه للتوافق مع الاسم القديم
class AllAccountsScreen extends InnovativeAllAccountsScreen {
  const AllAccountsScreen({super.key});
}

class _InnovativeAllAccountsScreenState
    extends State<InnovativeAllAccountsScreen> {
  final CustomCurrencyService _currencyService = CustomCurrencyService();
  String _currencySymbol = 'SAR';

  List<Account> filteredAccounts = [];
  bool isLoading = true;
  String searchQuery = '';
  String sortBy = 'name'; // name, balance, date
  bool sortAscending = true;

  /// الحصول على الهوامش المتجاوبة
  EdgeInsets _getResponsiveMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 600) {
      return const EdgeInsets.symmetric(horizontal: 32);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16);
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateService>().loadAllData();
      _loadCurrencySymbol();
    });
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await _currencyService.getDefaultCurrencySymbol();
    if (mounted) {
      setState(() {
        _currencySymbol = symbol;
      });
    }
  }

  void _filterAndSortAccounts(List<Account> accounts) {
    filteredAccounts =
        accounts.where((account) {
          return account.name.toLowerCase().contains(searchQuery.toLowerCase());
        }).toList();

    // ترتيب الحسابات
    filteredAccounts.sort((a, b) {
      int comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'balance':
          comparison = a.balance.compareTo(b.balance);
          break;
        case 'date':
          comparison = (a.createdAt ?? DateTime.now()).compareTo(
            b.createdAt ?? DateTime.now(),
          );
          break;
      }
      return sortAscending ? comparison : -comparison;
    });
  }

  void _changeSorting(String newSortBy) {
    setState(() {
      if (sortBy == newSortBy) {
        sortAscending = !sortAscending;
      } else {
        sortBy = newSortBy;
        sortAscending = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(),
      body: Consumer<AppStateService>(
        builder: (context, appState, child) {
          final accounts = appState.accounts;

          if (appState.isLoading) {
            isLoading = true;
          } else {
            isLoading = false;
            _filterAndSortAccounts(accounts);
          }

          return _buildBody(appState);
        },
      ),
      drawer: _buildDrawer(),
      floatingActionButton: _buildFAB(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color(0xFF2E7D32), // Dark green
              Color(0xFF4CAF50), // Green
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: const Text(
          'دفتر الحسابات',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      centerTitle: true,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xFF1976D2), // Dark blue
                Color(0xFF2196F3), // Blue
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF1976D2).withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.add_rounded, color: Colors.white, size: 22),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddAccountScreen(),
                ),
              );
              if (result != null && mounted) {
                context.read<AppStateService>().loadAllData();
              }
            },
          ),
        ),
      ],
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildBody(AppStateService appState) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 20),
          // كارت الإحصائيات السريعة
          _buildStatsCard(appState),
          const SizedBox(height: 20),
          // شريط البحث والتصفية
          _buildSearchAndFilter(),
          const SizedBox(height: 20),
          // الجدول
          Expanded(child: _buildAccountsTable()),
        ],
      ),
    );
  }

  /// بناء كارت الإحصائيات
  Widget _buildStatsCard(AppStateService appState) {
    final accounts = appState.accounts;
    final totalAccounts = accounts.length;
    final totalCredit = accounts
        .where((a) => a.balance > 0)
        .fold(0.0, (sum, a) => sum + a.balance);
    final totalDebit = accounts
        .where((a) => a.balance < 0)
        .fold(0.0, (sum, a) => sum + a.balance.abs());

    return Container(
      margin: _getResponsiveMargin(context),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4A5FBF).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'إجمالي الحسابات',
              totalAccounts.toString(),
              Icons.account_balance_wallet,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'لنا',
              '${totalCredit.toStringAsFixed(0)} $_currencySymbol',
              Icons.trending_up,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'علينا',
              '${totalDebit.toStringAsFixed(0)} $_currencySymbol',
              Icons.trending_down,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontFamily: 'Cairo',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث والتصفية
  Widget _buildSearchAndFilter() {
    return Container(
      margin: _getResponsiveMargin(context),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // حقل البحث
          TextField(
            onChanged: (value) {
              setState(() {
                searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: 'البحث في الحسابات...',
              hintStyle: const TextStyle(
                color: Color(0xFF6B7280),
                fontFamily: 'Cairo',
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.search_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: const Color(0xFFF9FAFB),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // أزرار التصفية
          Row(
            children: [
              const Text(
                'ترتيب حسب:',
                style: TextStyle(
                  color: Color(0xFF374151),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Row(
                  children: [
                    _buildSortButton('الاسم', 'name'),
                    const SizedBox(width: 8),
                    _buildSortButton('الرصيد', 'balance'),
                    const SizedBox(width: 8),
                    _buildSortButton('التاريخ', 'date'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortButton(String label, String value) {
    final isSelected = sortBy == value;
    return Expanded(
      child: GestureDetector(
        onTap: () => _changeSorting(value),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            gradient:
                isSelected
                    ? const LinearGradient(
                      colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
                    )
                    : null,
            color: isSelected ? null : const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? Colors.transparent : const Color(0xFFE5E7EB),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? Colors.white : const Color(0xFF374151),
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  fontFamily: 'Cairo',
                ),
              ),
              if (isSelected) ...[
                const SizedBox(width: 4),
                Icon(
                  sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  color: Colors.white,
                  size: 14,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء جدول الحسابات
  Widget _buildAccountsTable() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4A5FBF)),
              strokeWidth: 3,
            ),
            SizedBox(height: 16),
            Text(
              'جاري تحميل الحسابات...',
              style: TextStyle(
                color: Color(0xFF6B7280),
                fontSize: 16,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      );
    }

    if (filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF4A5FBF).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.search_off_rounded,
                size: 48,
                color: Color(0xFF4A5FBF),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              searchQuery.isEmpty ? 'لا توجد حسابات' : 'لا توجد نتائج للبحث',
              style: const TextStyle(
                color: Color(0xFF374151),
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              searchQuery.isEmpty
                  ? 'ابدأ بإضافة حساب جديد'
                  : 'جرب كلمات بحث أخرى',
              style: const TextStyle(
                color: Color(0xFF6B7280),
                fontSize: 14,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      margin: _getResponsiveMargin(context),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الجدول
          _buildTableHeader(),
          // محتوى الجدول
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: filteredAccounts.length,
              itemBuilder: (context, index) {
                return _buildTableRow(filteredAccounts[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الجدول
  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: GestureDetector(
              onTap: () => _changeSorting('name'),
              child: Row(
                children: [
                  const Text(
                    'اسم الحساب',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  if (sortBy == 'name') ...[
                    const SizedBox(width: 4),
                    Icon(
                      sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ],
                ],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _changeSorting('balance'),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'الرصيد',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  if (sortBy == 'balance') ...[
                    const SizedBox(width: 4),
                    Icon(
                      sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ],
                ],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _changeSorting('date'),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'التاريخ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  if (sortBy == 'date') ...[
                    const SizedBox(width: 4),
                    Icon(
                      sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف في الجدول
  Widget _buildTableRow(Account account, int index) {
    final isEven = index % 2 == 0;
    final isPositive = account.balance >= 0;
    final accountType = isPositive ? 'دائن' : 'مدين';
    final formattedDate = DateFormat(
      'yyyy/MM/dd',
    ).format(account.createdAt ?? DateTime.now());

    return GestureDetector(
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AccountDetailsScreen(account: account),
          ),
        );
        if (result == true && mounted) {
          context.read<AppStateService>().loadAllData();
        }
      },
      onLongPress: () => _showAccountActions(account),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isEven ? Colors.white : const Color(0xFFF8F9FA),
          border: const Border(
            bottom: BorderSide(color: Color(0xFFE5E7EB), width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // اسم الحساب مع أيقونة النوع
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color:
                          isPositive
                              ? const Color(0xFF10B981).withValues(alpha: 0.1)
                              : const Color(0xFFEF4444).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isPositive ? Icons.trending_up : Icons.trending_down,
                      color:
                          isPositive
                              ? const Color(0xFF10B981)
                              : const Color(0xFFEF4444),
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.name,
                          style: const TextStyle(
                            color: Color(0xFF1F2937),
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Cairo',
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isPositive
                                    ? const Color(
                                      0xFF10B981,
                                    ).withValues(alpha: 0.1)
                                    : const Color(
                                      0xFFEF4444,
                                    ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            accountType,
                            style: TextStyle(
                              color:
                                  isPositive
                                      ? const Color(0xFF10B981)
                                      : const Color(0xFFEF4444),
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // الرصيد
            Expanded(
              flex: 2,
              child: Text(
                '${account.balance.toStringAsFixed(0)} $_currencySymbol',
                style: TextStyle(
                  color:
                      isPositive
                          ? const Color(0xFF10B981)
                          : const Color(0xFFEF4444),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
                textAlign: TextAlign.center,
              ),
            ),
            // التاريخ
            Expanded(
              flex: 2,
              child: Text(
                formattedDate,
                style: const TextStyle(
                  color: Color(0xFF6B7280),
                  fontSize: 12,
                  fontFamily: 'Cairo',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض خيارات الحساب
  void _showAccountActions(Account account) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        account.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Expanded(
                            child: _buildActionButton(
                              'عرض التفاصيل',
                              Icons.visibility,
                              const Color(0xFF4A5FBF),
                              () async {
                                final appStateService =
                                    context.read<AppStateService>();
                                Navigator.pop(context);
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => AccountDetailsScreen(
                                          account: account,
                                        ),
                                  ),
                                );
                                if (result == true && mounted) {
                                  appStateService.loadAllData();
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              'إضافة معاملة',
                              Icons.add,
                              const Color(0xFF10B981),
                              () async {
                                final appStateService =
                                    context.read<AppStateService>();
                                Navigator.pop(context);
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) =>
                                            AddAmountScreen(account: account),
                                  ),
                                );
                                if (result == true && mounted) {
                                  appStateService.loadAllData();
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFAB() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4A5FBF).withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddAccountScreen()),
          );
          if (result != null && mounted) {
            context.read<AppStateService>().loadAllData();
          }
        },
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(Icons.add_rounded, color: Colors.white, size: 28),
      ),
    );
  }

  /// بناء القائمة الجانبية
  Widget _buildDrawer() {
    return Drawer(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
          ),
        ),
        child: Column(
          children: [
            // رأس القائمة
            Container(
              padding: const EdgeInsets.only(top: 50, bottom: 20),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet_rounded,
                      size: 48,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'دفتر الحسابات',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'إدارة حساباتك بسهولة',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
            ),
            // قائمة الخيارات
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: ListView(
                  padding: const EdgeInsets.only(top: 20),
                  children: [
                    _buildDrawerItem(
                      icon: Icons.home_rounded,
                      title: 'الشاشة الرئيسية',
                      subtitle: 'جميع الحسابات',
                      isSelected: true,
                      onTap: () => Navigator.pop(context),
                    ),
                    _buildDrawerItem(
                      icon: Icons.add_circle_rounded,
                      title: 'إضافة حساب جديد',
                      subtitle: 'إنشاء حساب عميل',
                      onTap: () async {
                        Navigator.pop(context);
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AddAccountScreen(),
                          ),
                        );
                        if (result != null && mounted) {
                          context.read<AppStateService>().loadAllData();
                        }
                      },
                    ),
                    _buildDrawerItem(
                      icon: Icons.assessment_rounded,
                      title: 'التقارير الشاملة',
                      subtitle: 'عرض التقارير المالية',
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const ComprehensiveReportsScreen(),
                          ),
                        );
                      },
                    ),
                    _buildDrawerItem(
                      icon: Icons.settings_rounded,
                      title: 'الإعدادات',
                      subtitle: 'إعدادات التطبيق',
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ModernSettingsScreen(),
                          ),
                        );
                      },
                    ),
                    _buildDrawerItem(
                      icon: Icons.currency_exchange_rounded,
                      title: 'إدارة العملات',
                      subtitle: 'إضافة وتعديل العملات',
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const CurrencyManagementScreen(),
                          ),
                        );
                      },
                    ),
                    _buildDrawerItem(
                      icon: Icons.category_rounded,
                      title: 'إدارة التصنيفات',
                      subtitle: 'تنظيم الحسابات بالتصنيفات',
                      onTap: () async {
                        Navigator.pop(context);
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const CategoriesManagementScreen(),
                          ),
                        );
                      },
                    ),
                    const Divider(height: 32),
                    _buildDrawerItem(
                      icon: Icons.info_rounded,
                      title: 'حول التطبيق',
                      subtitle: 'معلومات التطبيق',
                      onTap: () {
                        Navigator.pop(context);
                        _showAboutDialog();
                      },
                    ),
                    _buildDrawerItem(
                      icon: Icons.logout_rounded,
                      title: 'تسجيل الخروج',
                      subtitle: 'الخروج من التطبيق',
                      textColor: Colors.red,
                      onTap: () {
                        Navigator.pop(context);
                        _showLogoutDialog();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isSelected = false,
    Color? textColor,
  }) {
    final color =
        textColor ??
        (isSelected ? const Color(0xFF4A5FBF) : const Color(0xFF374151));

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color:
            isSelected ? const Color(0xFF4A5FBF).withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 14,
            fontFamily: 'Cairo',
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: color.withValues(alpha: 0.7),
            fontSize: 12,
            fontFamily: 'Cairo',
          ),
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'حول التطبيق',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'دفتر الحسابات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                SizedBox(height: 8),
                Text('الإصدار: 1.0.0', style: TextStyle(fontFamily: 'Cairo')),
                SizedBox(height: 16),
                Text(
                  'تطبيق لإدارة الحسابات المالية بسهولة وأمان. يمكنك من خلاله تتبع المعاملات المالية وإدارة حسابات العملاء.',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'موافق',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
            ],
          ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            content: const Text(
              'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.of(context).popUntil((route) => route.isFirst);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تسجيل الخروج بنجاح'),
                      backgroundColor: Color(0xFF4A5FBF),
                    ),
                  );
                },
                child: const Text(
                  'تسجيل الخروج',
                  style: TextStyle(color: Colors.red, fontFamily: 'Cairo'),
                ),
              ),
            ],
          ),
    );
  }
}
